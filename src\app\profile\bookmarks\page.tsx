'use client';

import React, { useState } from 'react';
import { useGetCircles } from '@/api/generated/ayafeedComponents';
import { CirclesList } from '@/components/circles/CirclesList';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Heart, Users } from 'lucide-react';

export default function BookmarksPage() {
  const [showBookmarkedOnly, setShowBookmarkedOnly] = useState(true);

  // 使用社团列表接口，支持收藏筛选
  const { data: circlesResponse, isLoading } = useGetCircles({
    queryParams: {
      page: '1',
      pageSize: '20',
      ...(showBookmarkedOnly && { bookmarkedOnly: 'true' })
    }
  });

  return (
    <div className="space-y-6">
      {/* 页面标题和说明 */}
      <div className="space-y-4">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent">
            我的收藏
          </h1>
          <p className="text-slate-600 dark:text-slate-400 mt-1">
            查看和管理您收藏的社团
          </p>
        </div>

        {/* 筛选控制 */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Heart className="h-5 w-5 text-red-500" />
              筛选选项
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Users className="h-4 w-4 text-slate-500" />
                <span className="text-sm font-medium">仅显示已收藏的社团</span>
              </div>
              <Switch
                checked={showBookmarkedOnly}
                onCheckedChange={setShowBookmarkedOnly}
              />
            </div>
            {!showBookmarkedOnly && (
              <p className="text-xs text-slate-500 mt-2">
                关闭此选项将显示所有社团，已收藏的会排在前面
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 社团列表 */}
      <div>
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-slate-500 mt-2">加载中...</p>
          </div>
        ) : circlesResponse?.items.length === 0 ? (
          <Card>
            <CardContent className="py-16 text-center">
              <Heart className="h-16 w-16 text-slate-300 dark:text-slate-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">
                {showBookmarkedOnly ? '暂无收藏' : '暂无社团'}
              </h3>
              <p className="text-slate-600 dark:text-slate-400">
                {showBookmarkedOnly
                  ? '前往社团列表页面开始收藏您感兴趣的社团吧'
                  : '暂时没有社团数据'
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          <CirclesList
            circles={circlesResponse?.items || []}
            isLoading={isLoading}
            showBookmarkButton={true}
          />
        )}
      </div>
    </div>
  );
}
