"use client";

import React from "react";

import "@/app/globals.css";
import AdminHeader from "@/components/admin/header";
import Sidebar from "@/components/admin/sidebar";
import BetterAuthRoleGuard from "@/components/BetterAuthRoleGuard";

// 后台管理系统 Layout
// 该布局仅作用于 /admin 路由下的所有页面
// 后续可在此处加入 Ant Design Menu、Sidebar 等导航组件
export default function AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <BetterAuthRoleGuard allow={["admin", "editor"]}>
      <div className="h-screen flex flex-col bg-background">
        <AdminHeader />
        <div className="flex flex-1 overflow-hidden">
          <Sidebar />
          <main className="flex-1 p-6 overflow-auto">{children}</main>
        </div>
      </div>
    </BetterAuthRoleGuard>
  );
} 