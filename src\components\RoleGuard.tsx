"use client";

import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useSession } from "@/hooks/useSession";
import type { UserRole } from "@/types/user";

interface RoleGuardProps {
  allow: UserRole[];
  /** 当权限不足时的替代渲染，默认跳转到 /403 */
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

/**
 * 重构的 RoleGuard 组件
 * 直接使用 Better Auth 的 useSession hook，避免与复杂的认证状态管理产生冲突
 */
export default function RoleGuard({ allow, children, fallback }: RoleGuardProps) {
  const router = useRouter();
  const { user, isLoading, error, isAuthenticated } = useSession();
  const [hasRedirected, setHasRedirected] = useState(false);

  console.debug('[RoleGuard] 状态:', {
    user: user ? { id: user.id, email: user.email, role: user.role } : null,
    isLoading,
    error,
    isAuthenticated,
    allowedRoles: allow,
    hasRedirected
  });

  useEffect(() => {
    // 防止重复重定向
    if (hasRedirected) {
      return;
    }

    // 如果还在加载中，等待
    if (isLoading) {
      console.debug('[RoleGuard] 正在加载用户状态...');
      return;
    }

    // 如果有错误或用户未登录，重定向到登录页
    if (error || !isAuthenticated || !user) {
      console.debug('[RoleGuard] 用户未登录或认证失败，重定向到登录页', { error, isAuthenticated, user });
      const currentPath = window.location.pathname + window.location.search;
      const loginUrl = `/login?redirect=${encodeURIComponent(currentPath)}`;
      setHasRedirected(true);
      router.replace(loginUrl);
      return;
    }

    // 检查用户权限
    if (!allow.includes(user.role)) {
      console.debug('[RoleGuard] 权限不足', { userRole: user.role, allowedRoles: allow });
      setHasRedirected(true);
      router.replace("/403");
      return;
    }

    console.debug('[RoleGuard] 权限检查通过', { userRole: user.role, allowedRoles: allow });
  }, [isLoading, user, error, isAuthenticated, allow, router, hasRedirected]);

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return fallback ?? (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">正在验证权限...</p>
        </div>
      </div>
    );
  }

  // 如果有错误或用户未登录，显示重定向状态
  if (error || !isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">正在跳转到登录页面...</p>
        </div>
      </div>
    );
  }

  // 如果权限不足，显示重定向状态
  if (!allow.includes(user.role)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">权限不足，正在跳转...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}