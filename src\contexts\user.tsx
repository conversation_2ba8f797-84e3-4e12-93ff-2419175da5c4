"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

import { useAuthStore } from '@/stores/auth';
import { authService } from '@/services/auth';

import type { User } from "@/types/user";

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (identifier: string, password: string) => Promise<void>; // 支持邮箱或用户名
  register: (credentials: { email: string; password: string; name?: string; username?: string }) => Promise<void>;
  logout: () => Promise<void>;
  ready: boolean; // Keep ready for compatibility
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const authStore = useAuthStore();
  const [user, setUser] = useState<User | null>(authStore.user);
  const [isLoading, setIsLoading] = useState(true);

  // 只在用户状态变化时打印日志（开发模式）
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.debug('[AuthProvider] 用户状态变化 - authStore.user:', authStore.user);
      console.debug('[AuthProvider] 用户状态变化 - authStore.user?.role:', authStore.user?.role);
      console.debug('[AuthProvider] 用户状态变化 - local user state:', user);
      console.debug('[AuthProvider] 用户状态变化 - local user state role:', user?.role);
    }
  }, [authStore.user, user]);

  // 监听 authStore 状态变化，确保 Context 状态与 Store 同步
  // 但是只有当 authStore.user 包含完整数据时才更新
  useEffect(() => {
    if (authStore.user?.role) {
      console.debug('[AuthProvider] authStore 状态变化，更新本地用户状态:', authStore.user);
      setUser(authStore.user);
    } else if (authStore.user === null) {
      console.debug('[AuthProvider] authStore 用户为 null，清除本地状态');
      setUser(null);
    } else {
      console.debug('[AuthProvider] authStore.user 缺少 role，忽略更新:', authStore.user);
    }
  }, [authStore.user, authStore.isAuthenticated]);

  useEffect(() => {
    let mounted = true;

    // 应用加载时，检查用户会话
    const checkUserSession = async () => {
      console.debug('[AuthProvider] 开始检查用户会话');

      // 检查是否刚刚登录成功
      const isJustLoggedIn = typeof window !== 'undefined' &&
        window.localStorage.getItem('isLoggedIn') === 'true';

      if (isJustLoggedIn) {
        console.debug('[AuthProvider] 检测到刚登录，清除登录标记，跳过 API 检查');
        window.localStorage.removeItem('isLoggedIn');

        // 刚登录的情况下，直接使用缓存的用户信息，不调用 API
        const cachedUser = authService.getUser();
        if (cachedUser && mounted) {
          console.debug('[AuthProvider] 使用刚登录的用户信息:', cachedUser);
          setUser(cachedUser);
          setIsLoading(false);
          return;
        }
      }

      // 先检查是否有缓存的用户信息
      const cachedUser = authService.getUser();
      if (cachedUser && mounted) {
        console.debug('[AuthProvider] 使用缓存的用户信息:', cachedUser);
        setUser(cachedUser);
        setIsLoading(false);
        return;
      }

      try {
        // 如果已经有用户状态，跳过认证检查，避免清除刚登录的状态
        if (authStore.user?.role) {
          console.debug('[AuthProvider] authStore 已有用户状态，跳过 API 检查');
          if (mounted) {
            setUser(authStore.user);
            setIsLoading(false);
          }
          return;
        }

        // 只有在没有任何用户状态的情况下，才调用 API 检查
        console.debug('[AuthProvider] 没有用户状态，调用 API 检查');
        await authStore.checkAuth(true); // silent = true

        if (mounted && authStore.user?.role) {
          setUser(authStore.user);
        }
      } catch (error) {
        console.debug('[AuthProvider] 认证检查失败:', error);
        if (mounted) {
          setUser(null);
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
          console.debug('[AuthProvider] 认证检查结束，isLoading: false');
        }
      }
    };

    checkUserSession();

    return () => {
      mounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 只在组件挂载时执行一次，避免重复的认证检查

  const login = async (identifier: string, password: string) => {
    // 使用 authStore 的登录方法
    await authStore.login({ identifier, password });

    // 登录成功后立即从 authService 获取最新的用户信息并设置到 Context
    const loggedInUser = authService.getUser();
    if (loggedInUser) {
      setUser(loggedInUser);
      // 登录成功后标记为已登录，5秒后自动清除标记
      if (typeof window !== 'undefined') {
        window.localStorage.setItem('isLoggedIn', 'true');
        // 5秒后清除标记，避免永久跳过401处理
        setTimeout(() => {
          window.localStorage.removeItem('isLoggedIn');
          console.debug('[AuthProvider] 清除登录标记');
        }, 5000);
      }
      console.debug('[AuthProvider] 登录成功，用户状态已设置:', loggedInUser);
    } else {
      throw new Error('登录状态获取失败');
    }
  };

  const register = async (credentials: { email: string; password: string; name?: string; username?: string }) => {
    await authStore.register(credentials);

    // 注册成功后立即从 authService 获取最新的用户信息
    const registeredUser = authService.getUser();
    if (registeredUser) {
      setUser(registeredUser);
    }
  };

  const logout = async () => {
    try {
      await authStore.logout();
    } catch (error) {
      // 登出错误不应该阻止用户界面更新
      console.warn('[AuthContext] 登出过程中出现错误，但继续清除本地状态:', error);
    } finally {
      setUser(null);
      // Force redirect to login page after logout
      if (typeof window !== "undefined") {
        window.location.href = "/login";
      }
    }
  };
  
  const value = {
    user,
    isLoading,
    isAuthenticated: authStore.isAuthenticated,
    login,
    register,
    logout,
    ready: !isLoading
  };

  // 移除重复的 Context value 日志

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 