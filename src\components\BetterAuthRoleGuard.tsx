"use client";

import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useSession as useBetterAuthSession } from "@/lib/auth-client";
import type { UserRole } from "@/types/user";

interface BetterAuthRoleGuardProps {
  allow: UserRole[];
  /** 当权限不足时的替代渲染，默认跳转到 /403 */
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

/**
 * 基于 Better Auth 的 RoleGuard 组件
 * 直接使用 Better Auth 的 useSession hook，完全独立于现有的认证状态管理
 * 避免与 AuthProvider、authStore 等产生冲突
 */
export default function BetterAuthRoleGuard({ allow, children, fallback }: BetterAuthRoleGuardProps) {
  const router = useRouter();
  const session = useBetterAuthSession();
  const [hasRedirected, setHasRedirected] = useState(false);

  // 从 Better Auth session 中提取用户信息
  const user = session.data?.user;
  const userRole = (user as any)?.role as UserRole | undefined;
  const isLoading = session.isPending;
  const error = session.error;
  const isAuthenticated = !!user;

  console.debug('[BetterAuthRoleGuard] Better Auth Session 状态:', {
    user: user ? { 
      id: user.id, 
      email: user.email, 
      name: user.name,
      role: userRole 
    } : null,
    isLoading,
    error: error?.message,
    isAuthenticated,
    allowedRoles: allow,
    hasRedirected
  });

  useEffect(() => {
    // 防止重复重定向
    if (hasRedirected) {
      return;
    }

    // 如果还在加载中，等待
    if (isLoading) {
      console.debug('[BetterAuthRoleGuard] 正在加载 Better Auth session...');
      return;
    }

    // 如果有错误或用户未登录，重定向到登录页
    if (error || !isAuthenticated || !user || !userRole) {
      console.debug('[BetterAuthRoleGuard] 用户未登录或认证失败，重定向到登录页', { 
        error: error?.message, 
        isAuthenticated, 
        hasUser: !!user,
        hasRole: !!userRole
      });
      const currentPath = window.location.pathname + window.location.search;
      const loginUrl = `/login?redirect=${encodeURIComponent(currentPath)}`;
      setHasRedirected(true);
      router.replace(loginUrl);
      return;
    }

    // 检查用户权限
    if (!allow.includes(userRole)) {
      console.debug('[BetterAuthRoleGuard] 权限不足', { userRole, allowedRoles: allow });
      setHasRedirected(true);
      router.replace("/403");
      return;
    }

    console.debug('[BetterAuthRoleGuard] 权限检查通过', { userRole, allowedRoles: allow });
  }, [isLoading, user, userRole, error, isAuthenticated, allow, router, hasRedirected]);

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return fallback ?? (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">正在验证权限...</p>
        </div>
      </div>
    );
  }

  // 如果有错误或用户未登录，显示重定向状态
  if (error || !isAuthenticated || !user || !userRole) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">正在跳转到登录页面...</p>
        </div>
      </div>
    );
  }

  // 如果权限不足，显示重定向状态
  if (!allow.includes(userRole)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">权限不足，正在跳转...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
